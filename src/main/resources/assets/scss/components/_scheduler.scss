@use "sass:color";
@use "variables";
@use "mixins";

.k-scheduler {
    border-color: transparent;
    box-shadow: unset;
    .k-slot-cell {
        text-align: left;
    }
    .k-scheduler-toolbar {
        padding-bottom: 30px;
    }
    .k-scheduler-footer {
        .k-header {
            display: none;
        }
    }
}

.k-loading-image {
    background-image: url("../images/loading_100px.svg");
}
.k-loading-color {
    background-color: #FFFFFF;
    opacity: .7;
}

.neb-event {
    padding: 4px 28px 4px 6px;
    top: -2px;
    font-size: 12px;
    position: absolute;
    left: 0;
    right: 0;
    .neb-event-wrapper {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 2px;
    }
    &.neb-event-draggable {
        padding: 6px 10px;
    }
    &.neb-event-without-secondary-button {
        padding: 4px 28px 4px 12px;
    }
}

.k-icon {
    z-index: 1;
}

.assignment-detail-action-icon {
    vertical-align: middle;
    height: 20px;
    margin-top: -2px;
    cursor: pointer;
}

.k-event-actions > .k-icon {
    cursor: default;
}

.k-event-actions > .k-i-arrow-60-right {
    padding-left: 2px;
}

.k-scheduler-table {
    tbody td{
    background-color: #F7F7F7 !important;
    }
    tbody td:nth-child(2n){
    background-color: #FFFFFF !important;
    }
    tbody td.dropTarget {
        background-color: color.adjust(#B2B2B2, $alpha: -0.5)  !important;
    }
    tbody {
        td:nth-child(4n+1){
            border-left-color: #e3e3e3 !important;
        }

        td {
            border-left-color: #fff !important;
        }
    }
}

.k-scheduler-header-wrap {
    td,th {
        font-weight: normal;
    }
}

.k-state-focused,
.kd-state-focuse, .k-list > .k-state-focused, .k-listview > .k-state-focused, .k-grid-header th.k-state-focused, td.k-state-focused {
    box-shadow: none;
}

.k-scheduler-refresh {
    border: 1px solid #dddddd;
    border-radius: 8px;
    padding: 0px 20px;
    .k-i-reload {
        transform: translateY(-1px);
    }
}

.k-event {
    border-color: #BEBEBE;
    background: #B2B2B2;
}

.k-nav-day {
    font-weight: bold;
}

.k-scheduler .k-scheduler-toolbar {
    padding-bottom: 40px;
}

.k-scheduler-toolbar .k-link {
    padding: 1px 20px;
    border-radius: 6px !important;
    font-weight: 600;
}

.k-event .k-resize-handle:after, .k-task-single .k-resize-handle:after {
    background-color: transparent;
}

.k-scheduler {
    .k-marquee-text  {
        font-weight: bold;
    }
    .k-state-selected, .k-state-selected:link, .k-state-selected:visited, .k-tool.k-state-selected, .k-list > .k-state-selected, .k-list > .k-state-highlight, .k-panel > .k-state-selected, .k-ghost-splitbar-vertical, .k-ghost-splitbar-horizontal, .k-draghandle.k-state-selected:hover, .k-scheduler .k-scheduler-toolbar .k-state-selected, .k-scheduler .k-today.k-state-selected, .k-marquee-color {
        color: #ffffff !important;
        background-color: #B2B2B2 !important;
        border-color: #B2B2B2 !important;
    }
}

.k-link.k-event-delete {
    display: inline;
    color: #ffffff;
}

.k-button {
    border-radius: 6px;
    font-size: 14px;
    box-shadow: none;
    cursor: pointer;
    line-height: 16px;
    letter-spacing: 1.2px;
    text-decoration: none;

    &.k-primary.k-scheduler-delete {
        color: white;
        padding: 6px 18px;
        background: variables.$primary;
        border: 1px solid variables.$primary;
        @include mixins.background-gradient(#A7C3FF, #266BCC);
        float: right;
    }

    &.k-scheduler-cancel {
        color: variables.$primary;
        padding: 6px 8px;
        background: transparent;
        border: 1px solid variables.$silver-chalice;
        font-weight: 600;
    }
}

.k-event-actions {
    top: 2px;

    .k-i-arrow-60-left {
        margin-top: -2px;
    }
}

.k-window-title {
    display: none;
}

.k-popup-message {
    padding-bottom: 30px;
}

.k-edit-buttons.k-state-default {
    padding: 15px;
    text-align: left;
}

