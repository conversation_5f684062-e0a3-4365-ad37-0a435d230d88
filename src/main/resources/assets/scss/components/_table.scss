@use "variables";

table {
    th {
        text-align: left;
    }
}

.k-pager-refresh {
    display: none;
}


.k-grid-content {
    overflow: visible;
    overflow-x: visible;
    overflow-y: visible;
}

.k-grid,
.k-grid-header,
.k-grid-pager {
    border-color: transparent;
    box-shadow: none;
}

.k-grid-header-wrap {
    border-color: transparent;
    border-top: 1px solid #E8EDF6 !important;
    border-bottom: 1px solid #E8EDF6 !important;
}

.k-grid-header th.k-header {
    font-weight: normal;
    border-left-color: transparent;
    padding: 15px 10px;
    color: variables.$silver-chalice;
    font-size: 13px;
    font-weight: bold;
}

.k-grid-content {
    td {
        border-left-color: transparent !important;
        padding: 11px 10px;
        border-bottom: 1px solid #E8EDF6 !important;
        word-break: keep-all;
        text-overflow: ellipsis;
        white-space: nowrap;
        b {
            color: #505a64 !important;
        }
        a {
            color: #505a64 !important;
        }
        .badge {
            color: white;
            border: 1px solid rgba(0,0,0,0.2);
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            padding: 0px;
            width: 140px;
            display: block;
            text-align: center;
            font-size: 12px;
            letter-spacing: 0.5px;
        }
    }
}
.k-grid-pager {
    margin-top: 30px;
}

.k-grid tr:hover {
    background-color: rgba(200, 200, 200, 0.15) !important;
}

.k-link.k-pager-nav.k-state-disabled {
    color: #B2B2B2;
}

.k-grid-header:first-child th.k-header:first-child, thead.k-grid-header th.k-header:first-child, .k-rtl thead.k-grid-header th.k-header:last-child {
    border-radius: 0px;
}

.k-filter-row {
    background-color: rgba(200, 200, 200, 0.15) !important;
    &:hover {
        background-color: rgba(200, 200, 200, 0.15) !important;
    }
    th {
        
        border-left-color: transparent !important;
        .k-filtercell {
            .k-autocomplete {
                border-color: #DDE0E3;
                border-radius: 6px;
                input {
                    box-shadow: none;
                    background-color: white;
                }
            }
            .k-dropdown-wrap, .k-button {
                border-color: transparent !important;
            }
        }
    }
}


.status.active {
    color: variables.$primary;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.status.inactive {
    color: variables.$danger;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.issue-table {
    min-width: 900px;
}

@media (max-width: 1200px) {
    .issue-table {
        padding-right: 40px;
    }
}

@media (max-width: 1024px) {
    ul.k-pager-numbers.k-reset {
        left: 5.1em !important;
    }
}