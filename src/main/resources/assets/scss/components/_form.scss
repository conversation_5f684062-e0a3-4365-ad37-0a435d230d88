@use "sass:color";

@use "variables";
@use "mixins";

* {
    outline: none;
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
color: variables.$silver-chalice;
}
::-moz-placeholder { /* Firefox 19+ */
color: variables.$silver-chalice;
}
:-ms-input-placeholder { /* IE 10+ */
color: variables.$silver-chalice;
}
:-moz-placeholder { /* Firefox 18- */
color: variables.$silver-chalice;
}

.input-popup {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 7px 20px !important;
    border-radius: 6px !important;
    border: 1px solid rgba(0,0,0,0.2) !important;
    display: inline-block;
    font-weight: normal;
    font-size: 14px;
    color: #505a64 !important;
    width: 100%;
    margin-right: 10px;
}

input[type=text],
input[type=password],
input[type=date],
input[type=search],
input[type=number],
form select,
textarea,
.select2-selection--single {
    border-radius: 10px;
    background: variables.$athens-gray;
    width: 100%;
    font-size: 14px;
    padding: 13px 16px;
    border: 0px;
    box-shadow: none;
    color: variables.$trout;
    box-sizing: border-box;
}

input.listsearch,
 input#listsearch {
    border-radius: 8px;
    background: #F7F8FA;
    width: 100%;
    height: 32px;
    font-size: 12px;
    padding: 5.5px 16px 5.5px 28px;
    border: 0px;
    box-shadow: none;
    color: #505A64;
    box-sizing: border-box;
    border: 1px solid #dddddd;
    margin: 7px 860px 40px 0px;
    background-image: url(../images/search.png);
    background-repeat: no-repeat;
    background-position: 8px center;
}

.select2-selection--single  {
    padding: 15.5px 16px !important;
}

.help-block {
    display: block;
    color: variables.$danger;
    font-weight: normal;
    font-size: 12px;
    text-transform: initial;
    &.black {
        color: variables.$gray;
    }
}

.select2-dropdown {
    margin-top: -5px;
    border: 0px !important;
    padding: 12px;
    padding-top: 0px;
    background-color: variables.$athens-gray !important;
    .select2-search--dropdown {
        border: 0;
        .select2-search__field {
            padding: 10px;
            width: 100%;
            box-sizing: border-box;
            outline: none;
            border: 0;
            margin: 10px;
            font-size: 14px;
            margin: 0px !important;
            margin-bottom: 10px;
            background: white;
        }
    }
    .select2-results__option {
        border-bottom: 0px solid color.adjust(variables.$athens-gray, $alpha: -0.9);
        color: rgba(78, 79, 82, 0.6);
        &:last-child {
            border-bottom: 0px;
        }
        &:hover {
            color: variables.$primary !important;
        }
    }
}

textarea {
    resize: none;
}

.kendo-select {
    width: 100%;
    margin-top: 5px;
    .k-dropdown-wrap {
        background-color: #f7f8fa;
    }
}
