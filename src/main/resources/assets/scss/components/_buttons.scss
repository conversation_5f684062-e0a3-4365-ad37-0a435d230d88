@use "mixins";
@use "variables";


.btn {
    background: variables.$primary;
    border-radius: 6px;
    font-size: 14px;
    padding: 12px 29px;
    box-shadow: none;
    color: white;
    cursor: pointer;
    display: inline-block;  
    line-height: 16px;
    letter-spacing: 1.2px;
    text-decoration: none;
    border: 0px;
    font-weight: 600;

    &.login {
        padding: 11px 40px !important;
        border-radius: 10px !important;
    }

    &.small {
        font-size: 12px;
        padding: 8px 10px;
    }
    &.primary {
        border: 1px solid variables.$primary;
        @include mixins.background-gradient(#A7C3FF, #266BCC);
        &.small {
            padding: 6px 18px;
            font-weight: 600;
        }

        &.disable {
           border: 1px solid #BCCDE6;
           @include mixins.background-gradient(#D6E3FF, #BED1ED);

            &:hover {
                opacity: 1;
            }
        }
    }

    &.secondary {
       color: variables.$primary;
       border: 1px solid variables.$silver-chalice;
       background: transparent;
       &.disable {
           color: #CD1212;
       }
       &.logout {
            padding: 6px 18px;
            font-weight: 600;
       }
       &.danger-font-color {
            color: #CD1212;
       }
    }

    &.success {
        border: 1px solid #1B8F22;
        @include mixins.background-gradient(#9FB83E, #1B8F22);
    }

    &.danger {
        border: 1px solid #CD1212;
        @include mixins.background-gradient(#F8703F, #CD1212);
    }

    &.link {
        background: transparent !important;
        padding: 0px;
        border-radius: 0px;
        letter-spacing: initial;
        border: 0px !important;
        &.primary {
            color: variables.$primary;
        }
    
        &.secondary {
            color: #E79632;
        }
    
        &.success {
            color: variables.$success;
        }
    
        &.danger {
            color: variables.$danger;
        }
        &:hover {
            text-decoration: underline;
        }
    }

    &:hover {
        opacity: 0.8
    }
}

.btn-popup {
    padding: 8px 20px;
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.2);
    display: inline-block;
    font-weight: bold;
    font-size: 14px;
    color: #266BCC;
    cursor: pointer;
    background-color: white;
    margin-right: 10px;
    &.danger {
        color: #D73B4F
    }
}