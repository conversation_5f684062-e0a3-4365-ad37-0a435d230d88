@use "variables";

.k-listview {
    border-color: transparent;
    border-top: 1px solid #e3e3e3;
    box-shadow: none;
}

.neb-listview-wrapper {
    border-bottom: 1px solid #e3e3e3;
    padding: 6px 0px;   
    min-height: 40px;
    small {
        font-weight: normal;
        text-transform: initial;
        font-size: 10px;
    }
}

.neb-listview-wrapper-small {
    border-bottom: 1px solid #e3e3e3;
    padding: 1px 0px;
    min-height: 40px;
    line-height: 3;
}

#issue-pager, #assistant-pager {
    border: 0px;
    box-shadow: none;
    margin-top: 15px;
}

.k-pager-numbers {
    padding: 0;
    margin: 0;
}

.listview .k-state-selected {
    background: transparent;
    color: #505a64;
    border-color: transparent;
}

.listview {
    .neb-listitem {
        cursor: pointer;
        .neb-listview-main {
           line-height: 20px;
           .neb-list-recurring-icon{
              margin-left: 2px;
              height: 20px;
              vertical-align: middle;
           }
           span {
            margin-left: 2px;
           }
        }
    }
}

.border-top-zero {
    border-top: 0px;
}
