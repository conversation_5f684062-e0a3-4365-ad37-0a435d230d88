@use "variables";
@use "mixins";

#navigation {
    background: white;
    font-size: 12px;
    color: variables.$trout;
    box-shadow: 0px 2px 4px #B0B0B0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999999;
    position: fixed;
    &.relative {
        position: relative;
        background: variables.$primary;
    }
    .logo {
        transform: translateY(-2px);
    }
    ul {
        @include mixins.reset;
        display: table;
        height: 64px;
        li {
            @include mixins.reset;
            position: relative;
            display: table-cell;
            vertical-align: middle;
            a {
                @include mixins.reset;
                font-size: 12px;
                color: variables.$silver-chalice;
                font-weight: 400;
                margin-right: 22px;
                display: block;
                letter-spacing: 2px;
                &:hover {
                    color: variables.$primary;
                }
            }
        }
    }
}