<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{default-template}">

<head>
    <title>Smartbricks Auth Check</title>

    <script src="https://cdn.auth0.com/js/auth0-spa-js/2.1/auth0-spa-js.production.js"></script>
</head>

<body>
<section layout:fragment="content">
    <div class="container-fluid">
        <div class="margin-bottom-30">
            <h1>Smartbricks Auth Check</h1>
        </div>

        <script th:inline="javascript">
            async function performAuthenticationCheck() {
                const unauthorizedTemplate = document.getElementById('unauthorized-template');
                const authorizedTemplate = document.getElementById('authorized-template');
                const contentTarget = document.getElementById('content-target');

                const auth0Config = /*[[${oauth2Information}]]*/ {
                    // This is a placeholder for static viewing
                    domain: 'YOUR_AUTH0_DOMAIN',
                    clientId: 'YOUR_AUTH0_CLIENT_ID',
                    redirectUri: 'YOUR_REDIRECT_URI'
                };

                const auth0Client = await auth0.createAuth0Client({
                    domain: auth0Config.domain,
                    clientId: auth0Config.clientId,
                    authorizationParams: {
                        redirect_uri: auth0Config.redirectUri,
                    }
                });

                try {
                    const isAuthenticated = await auth0Client.getTokenSilently();
                    console.log(isAuthenticated)

                    const content = authorizedTemplate.content.cloneNode(true);
                    contentTarget.innerHTML = '';
                    contentTarget.appendChild(content);
                } catch (error) {
                    console.log(error.error)
                    console.log(error)

                    const content = unauthorizedTemplate.content.cloneNode(true);
                    contentTarget.innerHTML = '';
                    contentTarget.appendChild(content);
                }
            }

            window.addEventListener('load', performAuthenticationCheck);
        </script>

        <div id="content-target">
            <p>Checking authentication status...</p>
        </div>

        <template id="authorized-template">
            <h2>✅ You are authenticated!</h2>
            <p>You have a valid session.</p>
        </template>

        <template id="unauthorized-template">
            <h2>❌ Authentication Required</h2>
            <p>You do not have an active session.</p>

            <p th:utext="#{smartbricks.notAuthorized}"></p>
        </template>
    </div>
</section>
</body>
</html>
