<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
</head>

<body>
<section layout:fragment="content" class="container padding-left-50 padding-right-50 padding-top-20">
    <div class="documents-page gr-12 no-gutter">
        <div class="table-container">
            <!-- Offa Documents -->
            <h3 class="margin-top-10 padding-bottom-10">Offa</h3>
            <div class="row grid_header grid_row_border">
                <div class="gr-2 grid_cell">
                    <small th:text="#{issue.documents.type}"></small>
                </div>
                <div class="gr-6 grid_cell">
                    <small th:text="#{issue.documents.title}"></small>
                </div>
                <div class="gr-2 grid_cell">
                    <small th:text="#{issue.documents.date}"></small>
                </div>
                <div class="gr-2 grid_cell text-right">
                    <small th:text="#{issue.documents.download}"></small>
                </div>
            </div>

            <div class="scrollable-container">
                <div class="gr-12 no-gutter" th:if="${issueDocuments.offaDocuments.size() > 0}"
                     th:each="documentListItem : ${issueDocuments.offaDocuments}">
                    <div class="row grid_row_border vertical-center">
                        <div class="gr-2 grid_cell">
                            <span th:text="#{'issue.documents.type.'+${documentListItem.documentType}}"></span>
                        </div>
                        <div class="gr-6 grid_cell bold">
                            <span th:text="${documentListItem.filename}"></span>
                        </div>
                        <div class="gr-2 grid_cell">
                            <span th:text="${documentListItem.updatedAt}"></span>
                        </div>
                        <div class="gr-2 grid_cell text-right full-line-height">
                            <form method="GET" th:action="'/documents/' + ${documentListItem.id}">
                                <img src="/assets/images/<EMAIL>" alt="download" height="32px"
                                     class="cursor-pointer" onclick="submit()">
                            </form>
                        </div>
                    </div>
                </div>

                <div class="gr-12 no-gutter" th:unless="${issueDocuments.offaDocuments.size() > 0}">
                    <div class="row grid_row_border">
                        <div class="gr-12 grid_cell">
                            <span th:text="#{issue.documents.emptyList}"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <!-- Smartbricks Documents -->
            <h3 class="margin-top-30 padding-bottom-5" th:text="#{smartbricks.title}"></h3>
            <div th:if="${hasSmartbricksAuthorization}">
                <div class="row grid_header grid_row_border">
                    <div class="gr-10 grid_cell">
                        <small th:text="#{issue.documents.title}"></small>
                    </div>
                    <div class="gr-2 grid_cell text-right">
                        <small th:text="#{issue.documents.download}"></small>
                    </div>
                </div>

                <div class="scrollable-container">
                    <div class="gr-12 no-gutter" th:if="${issueDocuments.smartbricksDocuments.size() > 0}"
                         th:each="documentListItem : ${issueDocuments.smartbricksDocuments}">
                        <div class="row grid_row_border vertical-center">
                            <div class="gr-10 grid_cell bold">
                                <span th:text="${documentListItem.name}"></span>
                            </div>
                            <div class="gr-2 grid_cell text-right full-line-height">
                                <a th:href="@{/admin/api/v1/smartbricks/download(signedUrl=${documentListItem.signedUrl}, filename=${documentListItem.name})}">
                                    <img src="/assets/images/<EMAIL>" alt="download" height="32px"
                                         class="cursor-pointer">
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="gr-12 no-gutter" th:unless="${issueDocuments.smartbricksDocuments.size() > 0}">
                        <div class="row grid_row_border">
                            <div class="gr-12 grid_cell">
                                <span th:text="#{issue.documents.emptyList}"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div th:unless="${hasSmartbricksAuthorization}">
                <p th:utext="#{smartbricks.notAuthorized}"></p>
            </div>
        </div>
    </div>
</section>
</body>
</html>
